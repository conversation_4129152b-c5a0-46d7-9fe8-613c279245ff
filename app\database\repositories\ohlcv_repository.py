"""
Repository for OHLCV data operations with TimescaleDB optimizations.
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, asc, text, func
from datetime import datetime, timedelta
import pandas as pd

from app.database.models import StockOHLCV, StockOHLCVAgg, Symbol
from app.database.repositories.base import BaseRepository


class OHLCVRepository(BaseRepository[StockOHLCV]):
    """Repository for OHLCV data operations."""
    
    def __init__(self, db: Session):
        super().__init__(StockOHLCV, db)
    
    def insert_ohlcv_data(self, symbol_id: int, ohlcv_data: List[Dict[str, Any]]) -> int:
        """
        Bulk insert OHLCV data.
        
        Args:
            symbol_id: Symbol ID
            ohlcv_data: List of OHLCV data dictionaries
            
        Returns:
            Number of records inserted
        """
        records = []
        for data in ohlcv_data:
            record = StockOHLCV(
                symbol_id=symbol_id,
                timestamp=data['timestamp'],
                open=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close'],
                volume=data['volume']
            )
            records.append(record)
        
        if records:
            self.db.bulk_save_objects(records)
            self.db.commit()
        
        return len(records)
    
    def get_ohlcv_data(
        self,
        symbol_id: int,
        start_time: datetime,
        end_time: datetime,
        limit: Optional[int] = None
    ) -> List[StockOHLCV]:
        """
        Get OHLCV data for a symbol within time range.
        
        Args:
            symbol_id: Symbol ID
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum number of records
            
        Returns:
            List of OHLCV records
        """
        query = self.db.query(StockOHLCV).filter(
            and_(
                StockOHLCV.symbol_id == symbol_id,
                StockOHLCV.timestamp >= start_time,
                StockOHLCV.timestamp <= end_time
            )
        ).order_by(StockOHLCV.timestamp)
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_latest_ohlcv(self, symbol_id: int, count: int = 1) -> List[StockOHLCV]:
        """
        Get latest OHLCV records for a symbol.
        
        Args:
            symbol_id: Symbol ID
            count: Number of latest records to fetch
            
        Returns:
            List of latest OHLCV records
        """
        return self.db.query(StockOHLCV).filter(
            StockOHLCV.symbol_id == symbol_id
        ).order_by(desc(StockOHLCV.timestamp)).limit(count).all()
    
    def get_ohlcv_dataframe(
        self,
        symbol_id: int,
        start_time: datetime,
        end_time: datetime
    ) -> pd.DataFrame:
        """
        Get OHLCV data as pandas DataFrame.
        
        Args:
            symbol_id: Symbol ID
            start_time: Start timestamp
            end_time: End timestamp
            
        Returns:
            DataFrame with OHLCV data
        """
        query = text("""
            SELECT timestamp, open, high, low, close, volume
            FROM stock_ohlcv
            WHERE symbol_id = :symbol_id
                AND timestamp >= :start_time
                AND timestamp <= :end_time
            ORDER BY timestamp
        """)
        
        result = self.db.execute(query, {
            'symbol_id': symbol_id,
            'start_time': start_time,
            'end_time': end_time
        })
        
        df = pd.DataFrame(result.fetchall(), columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume'
        ])
        
        if not df.empty:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        return df
    
    def delete_old_data(self, days_to_keep: int = 90) -> int:
        """
        Delete old OHLCV data beyond specified days.
        
        Args:
            days_to_keep: Number of days to keep
            
        Returns:
            Number of records deleted
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        deleted_count = self.db.query(StockOHLCV).filter(
            StockOHLCV.timestamp < cutoff_date
        ).count()
        
        self.db.query(StockOHLCV).filter(
            StockOHLCV.timestamp < cutoff_date
        ).delete()
        
        self.db.commit()
        return deleted_count
    
    def get_data_range(self, symbol_id: int) -> Optional[Tuple[datetime, datetime]]:
        """
        Get the date range of available data for a symbol.
        
        Args:
            symbol_id: Symbol ID
            
        Returns:
            Tuple of (min_date, max_date) or None if no data
        """
        result = self.db.query(
            func.min(StockOHLCV.timestamp),
            func.max(StockOHLCV.timestamp)
        ).filter(StockOHLCV.symbol_id == symbol_id).first()
        
        if result and result[0] and result[1]:
            return result[0], result[1]
        return None
    
    def get_missing_data_gaps(
        self,
        symbol_id: int,
        start_time: datetime,
        end_time: datetime,
        expected_interval_minutes: int = 1
    ) -> List[Tuple[datetime, datetime]]:
        """
        Find gaps in OHLCV data.
        
        Args:
            symbol_id: Symbol ID
            start_time: Start timestamp
            end_time: End timestamp
            expected_interval_minutes: Expected interval between records
            
        Returns:
            List of (gap_start, gap_end) tuples
        """
        query = text("""
            WITH time_series AS (
                SELECT timestamp,
                       LAG(timestamp) OVER (ORDER BY timestamp) as prev_timestamp
                FROM stock_ohlcv
                WHERE symbol_id = :symbol_id
                    AND timestamp >= :start_time
                    AND timestamp <= :end_time
                ORDER BY timestamp
            )
            SELECT prev_timestamp + INTERVAL '1 minute' as gap_start,
                   timestamp - INTERVAL '1 minute' as gap_end
            FROM time_series
            WHERE prev_timestamp IS NOT NULL
                AND timestamp - prev_timestamp > INTERVAL ':interval minutes'
        """)
        
        result = self.db.execute(query, {
            'symbol_id': symbol_id,
            'start_time': start_time,
            'end_time': end_time,
            'interval': expected_interval_minutes
        })
        
        return [(row[0], row[1]) for row in result.fetchall()]


class OHLCVAggRepository(BaseRepository[StockOHLCVAgg]):
    """Repository for aggregated OHLCV data operations."""
    
    def __init__(self, db: Session):
        super().__init__(StockOHLCVAgg, db)
    
    def get_aggregated_data(
        self,
        symbol_id: int,
        timeframe: str,
        start_time: datetime,
        end_time: datetime,
        limit: Optional[int] = None
    ) -> List[StockOHLCVAgg]:
        """
        Get aggregated OHLCV data.
        
        Args:
            symbol_id: Symbol ID
            timeframe: Timeframe (5m, 15m, 30m, 1h, 1d)
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum number of records
            
        Returns:
            List of aggregated OHLCV records
        """
        query = self.db.query(StockOHLCVAgg).filter(
            and_(
                StockOHLCVAgg.symbol_id == symbol_id,
                StockOHLCVAgg.timeframe == timeframe,
                StockOHLCVAgg.timestamp >= start_time,
                StockOHLCVAgg.timestamp <= end_time
            )
        ).order_by(StockOHLCVAgg.timestamp)
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def insert_aggregated_data(
        self,
        symbol_id: int,
        timeframe: str,
        agg_data: List[Dict[str, Any]]
    ) -> int:
        """
        Bulk insert aggregated OHLCV data.
        
        Args:
            symbol_id: Symbol ID
            timeframe: Timeframe
            agg_data: List of aggregated data dictionaries
            
        Returns:
            Number of records inserted
        """
        records = []
        for data in agg_data:
            record = StockOHLCVAgg(
                symbol_id=symbol_id,
                timeframe=timeframe,
                timestamp=data['timestamp'],
                open=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close'],
                volume=data['volume']
            )
            records.append(record)
        
        if records:
            self.db.bulk_save_objects(records)
            self.db.commit()
        
        return len(records)
    
    def aggregate_data_using_procedure(
        self,
        symbol_id: int,
        timeframe: str,
        start_time: datetime,
        end_time: datetime
    ) -> None:
        """
        Aggregate data using stored procedure.
        
        Args:
            symbol_id: Symbol ID
            timeframe: Timeframe
            start_time: Start timestamp
            end_time: End timestamp
        """
        query = text("""
            SELECT aggregate_ohlcv_data(:symbol_id, :timeframe, :start_time, :end_time)
        """)
        
        self.db.execute(query, {
            'symbol_id': symbol_id,
            'timeframe': timeframe,
            'start_time': start_time,
            'end_time': end_time
        })
        
        self.db.commit()
