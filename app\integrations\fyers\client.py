"""
Fyers API client for market data and trading operations.
"""

import time
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd

from app.core.config import settings, load_yaml_config
from app.core.logging import get_logger
from app.integrations.fyers.auth import FyersAuth
from app.integrations.fyers.models import (
    QuoteData, OHLCVData, SymbolInfo, OrderRequest, OrderResponse,
    Position, FundsInfo, TimeFrame
)

logger = get_logger(__name__)


class FyersClient:
    """Fyers API client for market data and trading operations."""
    
    # Interval mapping for Fyers API
    INTERVAL_MAP = {
        "1": "1",      # 1 minute
        "3": "3",      # 3 minutes
        "5": "5",      # 5 minutes
        "15": "15",    # 15 minutes
        "30": "30",    # 30 minutes
        "60": "60",    # 60 minutes
        "1D": "1D",    # 1 day
        "1W": "1W"     # 1 week
    }
    
    def __init__(self, auth_dir: Optional[str] = None):
        """
        Initialize Fyers client.
        
        Args:
            auth_dir: Directory for authentication files
        """
        self.auth = FyersAuth(auth_dir)
        self.fyers_api = None
        self.access_token = None
        
        # Load rate limiting configuration
        config = load_yaml_config()
        rate_limit_config = config.get('rate_limit', {})
        self.min_delay = rate_limit_config.get('min_delay_seconds', 0.1)
        self.max_retries = rate_limit_config.get('max_retries', 5)
        self.retry_backoff = rate_limit_config.get('retry_backoff', 3.0)
    
    def authenticate(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication...")
            self.access_token = self.auth.authenticate()
            
            if self.access_token:
                # Initialize Fyers API client
                from fyers_apiv3.FyersModel import FyersModel
                
                self.fyers_api = FyersModel(
                    client_id=settings.fyers.client_id,
                    is_async=False,
                    token=self.access_token
                )
                
                logger.info("Fyers authentication successful")
                return True
            else:
                logger.error("Failed to get access token")
                return False
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, QuoteData]:
        """
        Get market quotes for multiple symbols with rate limiting.
        
        Args:
            symbols: List of symbol strings
            
        Returns:
            Dictionary mapping symbol to QuoteData
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return {}
        
        if not symbols:
            return {}
        
        quotes_data = {}
        batch_size = 50  # Fyers API recommendation
        total_batches = (len(symbols) + batch_size - 1) // batch_size
        
        logger.info(f"Fetching quotes for {len(symbols)} symbols in {total_batches} batches")
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch_symbols)} symbols)")
            
            # Retry logic for rate limiting
            for attempt in range(self.max_retries + 1):
                try:
                    # Prepare symbols for Fyers API
                    symbol_string = ",".join(batch_symbols)
                    
                    # Get quotes from Fyers API
                    response = self.fyers_api.quotes({"symbols": symbol_string})
                    
                    if response and response.get("code") == 200:
                        batch_quotes = response.get("d", {})
                        
                        # Process quotes data
                        for symbol in batch_symbols:
                            if symbol in batch_quotes:
                                try:
                                    quote_data = QuoteData.from_fyers_response(
                                        symbol, batch_quotes[symbol]
                                    )
                                    quotes_data[symbol] = quote_data
                                except Exception as e:
                                    logger.error(f"Failed to process quote for {symbol}: {e}")
                        
                        # Rate limiting
                        time.sleep(self.min_delay)
                        break
                        
                    elif response and response.get("code") == 429:
                        # Rate limit hit
                        if attempt < self.max_retries:
                            wait_time = self.retry_backoff * (2 ** attempt)
                            logger.warning(f"Rate limit hit for batch {batch_num}, "
                                         f"retrying after {wait_time:.1f} seconds...")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"Exceeded max retries for batch {batch_num}")
                            break
                    else:
                        logger.error(f"Failed to get quotes for batch {batch_num}: {response}")
                        break
                        
                except Exception as e:
                    logger.error(f"Error processing batch {batch_num}: {e}")
                    break
        
        logger.info(f"Successfully fetched quotes for {len(quotes_data)} symbols")
        return quotes_data
    
    def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[OHLCVData]:
        """
        Get historical OHLCV data for a symbol.
        
        Args:
            symbol: Symbol string
            timeframe: Timeframe (1, 5, 15, 30, 60, 1D)
            start_date: Start date
            end_date: End date
            
        Returns:
            List of OHLCVData
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return []
        
        try:
            # Convert timeframe
            interval = self.INTERVAL_MAP.get(timeframe, timeframe)
            
            # Prepare request data
            data = {
                "symbol": symbol,
                "resolution": interval,
                "date_format": "1",
                "range_from": start_date.strftime("%Y-%m-%d"),
                "range_to": end_date.strftime("%Y-%m-%d"),
                "cont_flag": "1"
            }
            
            logger.info(f"Fetching historical data for {symbol} ({timeframe}) "
                       f"from {start_date.date()} to {end_date.date()}")
            
            response = self.fyers_api.history(data)
            
            if response and response.get("code") == 200:
                candles = response.get("data", {}).get("candles", [])
                
                ohlcv_data = []
                for candle in candles:
                    try:
                        ohlcv = OHLCVData.from_fyers_response(candle)
                        ohlcv_data.append(ohlcv)
                    except Exception as e:
                        logger.error(f"Failed to process candle data: {e}")
                
                logger.info(f"Retrieved {len(ohlcv_data)} historical records for {symbol}")
                return ohlcv_data
            else:
                logger.error(f"Failed to get historical data for {symbol}: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return []
    
    def get_historical_data_as_dataframe(
        self,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """
        Get historical data as pandas DataFrame.
        
        Args:
            symbol: Symbol string
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            DataFrame with OHLCV data
        """
        ohlcv_data = self.get_historical_data(symbol, timeframe, start_date, end_date)
        
        if not ohlcv_data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        data_dict = {
            'timestamp': [d.timestamp for d in ohlcv_data],
            'open': [d.open for d in ohlcv_data],
            'high': [d.high for d in ohlcv_data],
            'low': [d.low for d in ohlcv_data],
            'close': [d.close for d in ohlcv_data],
            'volume': [d.volume for d in ohlcv_data]
        }
        
        df = pd.DataFrame(data_dict)
        df.set_index('timestamp', inplace=True)
        return df
    
    def place_order(self, order_request: OrderRequest) -> Optional[OrderResponse]:
        """
        Place an order.
        
        Args:
            order_request: Order request data
            
        Returns:
            OrderResponse if successful, None otherwise
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return None
        
        try:
            order_data = order_request.to_fyers_format()
            logger.info(f"Placing order: {order_request.symbol} {order_request.side.value} "
                       f"{order_request.quantity} @ {order_request.price}")
            
            response = self.fyers_api.place_order(order_data)
            
            if response and response.get("code") == 200:
                order_response = OrderResponse.from_fyers_response(response.get("data", {}))
                logger.info(f"Order placed successfully: {order_response.order_id}")
                return order_response
            else:
                logger.error(f"Failed to place order: {response}")
                return None
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    def get_positions(self) -> List[Position]:
        """
        Get current positions.
        
        Returns:
            List of Position objects
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return []
        
        try:
            response = self.fyers_api.positions()
            
            if response and response.get("code") == 200:
                positions_data = response.get("data", {}).get("netPositions", [])
                
                positions = []
                for pos_data in positions_data:
                    try:
                        position = Position.from_fyers_response(pos_data)
                        positions.append(position)
                    except Exception as e:
                        logger.error(f"Failed to process position data: {e}")
                
                return positions
            else:
                logger.error(f"Failed to get positions: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def get_funds(self) -> Optional[FundsInfo]:
        """
        Get account funds information.
        
        Returns:
            FundsInfo if successful, None otherwise
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return None
        
        try:
            response = self.fyers_api.funds()
            
            if response and response.get("code") == 200:
                funds_data = response.get("data", {})
                return FundsInfo.from_fyers_response(funds_data)
            else:
                logger.error(f"Failed to get funds: {response}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting funds: {e}")
            return None
    
    def get_profile(self) -> Optional[Dict[str, Any]]:
        """
        Get user profile information.
        
        Returns:
            Profile data if successful, None otherwise
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return None
        
        try:
            response = self.fyers_api.get_profile()
            
            if response and response.get("code") == 200:
                return response.get("data", {})
            else:
                logger.error(f"Failed to get profile: {response}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting profile: {e}")
            return None

    def is_authenticated(self) -> bool:
        """Check if client is authenticated."""
        return self.fyers_api is not None and self.access_token is not None
