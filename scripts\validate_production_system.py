#!/usr/bin/env python3
"""
Production system validation script for Signal Stack Trading Platform.
This script validates the complete system with real Fyers API data for NIFTY.

IMPORTANT: This script uses REAL market data and API calls.
Ensure you have valid Fyers credentials and the market is open for live testing.
"""

import sys
import os
import time
import json
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.connection import get_db, check_database_connection
from app.database.init_db import setup_database
from app.services.market_data_service import MarketDataService
from app.services.aggregation_service import AggregationService
from app.services.realtime_pipeline import RealTimeDataPipeline
from app.database.models import MarketType

logger = setup_logging(level="INFO")


class ProductionValidator:
    """Production system validator."""
    
    def __init__(self):
        """Initialize the validator."""
        self.test_results = {}
        self.market_service = None
        self.aggregation_service = None
        self.pipeline = None
        
        # Test configuration
        self.test_symbol_fyers = "NSE:NIFTY50-INDEX"
        self.test_symbol_db = "NIFTY"
        self.validation_duration = 180  # 3 minutes for real-time test
    
    def run_validation(self) -> bool:
        """
        Run complete production validation.
        
        Returns:
            True if all validations pass, False otherwise
        """
        logger.info("=" * 80)
        logger.info("PRODUCTION SYSTEM VALIDATION")
        logger.info("=" * 80)
        logger.info("⚠️  WARNING: This uses REAL market data and API calls")
        logger.info("⚠️  Ensure you have valid Fyers credentials")
        logger.info("=" * 80)
        
        validation_steps = [
            ("Database Connection", self._validate_database),
            ("Database Schema", self._validate_schema),
            ("Symbol Setup", self._validate_symbol_setup),
            ("Fyers Authentication", self._validate_fyers_auth),
            ("Historical Data Fetch", self._validate_historical_data),
            ("Data Storage", self._validate_data_storage),
            ("Timeframe Aggregation", self._validate_aggregation),
            ("Real-time Data Pipeline", self._validate_realtime_pipeline),
            ("Data Integrity", self._validate_data_integrity),
            ("Performance Metrics", self._validate_performance)
        ]
        
        passed_tests = 0
        total_tests = len(validation_steps)
        
        for step_name, step_func in validation_steps:
            logger.info(f"\n--- {step_name} Validation ---")
            
            try:
                start_time = time.time()
                result = step_func()
                duration = time.time() - start_time
                
                self.test_results[step_name] = {
                    'passed': result,
                    'duration': duration,
                    'timestamp': datetime.now()
                }
                
                if result:
                    logger.info(f"✅ {step_name}: PASSED ({duration:.2f}s)")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {step_name}: FAILED ({duration:.2f}s)")
                    
            except Exception as e:
                logger.error(f"💥 {step_name}: CRASHED - {e}")
                self.test_results[step_name] = {
                    'passed': False,
                    'duration': 0,
                    'error': str(e),
                    'timestamp': datetime.now()
                }
        
        # Generate validation report
        self._generate_validation_report(passed_tests, total_tests)
        
        return passed_tests == total_tests
    
    def _validate_database(self) -> bool:
        """Validate database connection and TimescaleDB."""
        try:
            # Check basic connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            # Check TimescaleDB extension
            db = next(get_db())
            result = db.execute("SELECT 1 FROM pg_extension WHERE extname = 'timescaledb'").fetchone()
            
            if not result:
                logger.error("TimescaleDB extension not found")
                return False
            
            logger.info("✓ Database connection and TimescaleDB validated")
            return True
            
        except Exception as e:
            logger.error(f"Database validation failed: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    def _validate_schema(self) -> bool:
        """Validate database schema and tables."""
        try:
            setup_database()
            
            db = next(get_db())
            
            # Check required tables
            required_tables = [
                'symbols', 'stock_ohlcv', 'stock_ohlcv_agg',
                'strategies', 'screener_results', 'backtest_results'
            ]
            
            for table in required_tables:
                result = db.execute(f"SELECT 1 FROM information_schema.tables WHERE table_name = '{table}'").fetchone()
                if not result:
                    logger.error(f"Required table '{table}' not found")
                    return False
            
            # Check hypertables
            hypertables = ['stock_ohlcv', 'stock_ohlcv_agg']
            for table in hypertables:
                result = db.execute(f"SELECT 1 FROM timescaledb_information.hypertables WHERE hypertable_name = '{table}'").fetchone()
                if not result:
                    logger.error(f"Hypertable '{table}' not found")
                    return False
            
            logger.info("✓ Database schema validated")
            return True
            
        except Exception as e:
            logger.error(f"Schema validation failed: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    def _validate_symbol_setup(self) -> bool:
        """Validate symbol setup."""
        try:
            db = next(get_db())
            self.market_service = MarketDataService(db)
            
            # Check if NIFTY symbol exists, create if not
            symbol = self.market_service.data_service.get_symbol_by_name(self.test_symbol_db)
            
            if not symbol:
                logger.info("Creating NIFTY symbol...")
                symbol = self.market_service.create_symbol_from_fyers(
                    self.test_symbol_db, MarketType.INDEX
                )
                
                if not symbol:
                    logger.error("Failed to create NIFTY symbol")
                    return False
            
            logger.info(f"✓ Symbol validated: {symbol.symbol} (ID: {symbol.id})")
            return True
            
        except Exception as e:
            logger.error(f"Symbol setup validation failed: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    def _validate_fyers_auth(self) -> bool:
        """Validate Fyers API authentication."""
        try:
            if not self.market_service:
                db = next(get_db())
                self.market_service = MarketDataService(db)
            
            # Test authentication
            if not self.market_service.initialize_fyers_connection():
                logger.error("Fyers authentication failed")
                return False
            
            # Test profile retrieval
            profile = self.market_service.fyers_client.get_profile()
            if not profile:
                logger.error("Failed to retrieve Fyers profile")
                return False
            
            logger.info(f"✓ Fyers authentication successful: {profile.get('name', 'Unknown')}")
            return True
            
        except Exception as e:
            logger.error(f"Fyers authentication validation failed: {e}")
            return False
    
    def _validate_historical_data(self) -> bool:
        """Validate historical data fetching."""
        try:
            # Fetch 3 days of historical data
            success = self.market_service.fetch_and_store_historical_data(
                symbol=self.test_symbol_fyers,
                timeframe="1",
                days=3
            )
            
            if not success:
                logger.error("Historical data fetch failed")
                return False
            
            # Verify data was stored
            stats = self.market_service.data_service.get_data_statistics(self.test_symbol_db)
            
            if not stats or stats['total_records'] == 0:
                logger.error("No historical data found in database")
                return False
            
            logger.info(f"✓ Historical data validated: {stats['total_records']} records")
            logger.info(f"  Data range: {stats['data_range']['start']} to {stats['data_range']['end']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Historical data validation failed: {e}")
            return False
    
    def _validate_data_storage(self) -> bool:
        """Validate data storage and retrieval."""
        try:
            # Test data retrieval
            end_time = datetime.now()
            start_time = end_time - timedelta(days=1)
            
            data = self.market_service.data_service.get_ohlcv_data(
                self.test_symbol_db, start_time, end_time, as_dataframe=True
            )
            
            if data is None or data.empty:
                logger.error("Failed to retrieve stored data")
                return False
            
            # Validate data structure
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    logger.error(f"Missing column in data: {col}")
                    return False
            
            logger.info(f"✓ Data storage validated: {len(data)} records retrieved")
            return True
            
        except Exception as e:
            logger.error(f"Data storage validation failed: {e}")
            return False
    
    def _validate_aggregation(self) -> bool:
        """Validate timeframe aggregation."""
        try:
            db = next(get_db())
            self.aggregation_service = AggregationService(db)
            
            # Test aggregation for multiple timeframes
            timeframes = ['5m', '15m', '30m', '1h']
            
            for timeframe in timeframes:
                success = self.aggregation_service.aggregate_symbol_data(
                    symbol=self.test_symbol_db,
                    timeframe=timeframe,
                    start_time=datetime.now() - timedelta(days=3),
                    end_time=datetime.now()
                )
                
                if not success:
                    logger.error(f"Aggregation failed for {timeframe}")
                    return False
            
            # Verify aggregated data
            stats = self.aggregation_service.get_aggregation_statistics()
            
            for timeframe in timeframes:
                key = f'{timeframe}_records'
                if key not in stats or stats[key] == 0:
                    logger.error(f"No aggregated data found for {timeframe}")
                    return False
            
            logger.info("✓ Timeframe aggregation validated")
            for timeframe in timeframes:
                count = stats.get(f'{timeframe}_records', 0)
                logger.info(f"  {timeframe}: {count} records")
            
            return True
            
        except Exception as e:
            logger.error(f"Aggregation validation failed: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    def _validate_realtime_pipeline(self) -> bool:
        """Validate real-time data pipeline."""
        try:
            # Initialize pipeline
            self.pipeline = RealTimeDataPipeline()
            
            if not self.pipeline.initialize():
                logger.error("Pipeline initialization failed")
                return False
            
            # Track received data
            received_data = []
            
            def data_callback(symbol, quote_data):
                received_data.append((symbol, quote_data))
                if len(received_data) <= 5:  # Log first 5 updates
                    logger.info(f"Real-time: {symbol} = {quote_data.ltp}")
            
            self.pipeline.add_data_callback(data_callback)
            
            # Start pipeline
            if not self.pipeline.start([self.test_symbol_fyers]):
                logger.error("Pipeline start failed")
                return False
            
            logger.info(f"Real-time pipeline running for {self.validation_duration} seconds...")
            
            # Monitor for specified duration
            start_time = time.time()
            while time.time() - start_time < self.validation_duration:
                time.sleep(5)
                
                # Log statistics every 30 seconds
                if int(time.time() - start_time) % 30 == 0:
                    stats = self.pipeline.get_statistics()
                    logger.info(f"Pipeline stats: Received={stats['ticks_received']}, "
                               f"Processed={stats['ticks_processed']}, Errors={stats['errors']}")
            
            # Stop pipeline
            self.pipeline.stop()
            
            # Validate results
            if len(received_data) == 0:
                logger.warning("⚠️  No real-time data received (market may be closed)")
                return True  # Don't fail if market is closed
            
            logger.info(f"✓ Real-time pipeline validated: {len(received_data)} updates received")
            return True
            
        except Exception as e:
            logger.error(f"Real-time pipeline validation failed: {e}")
            return False
    
    def _validate_data_integrity(self) -> bool:
        """Validate data integrity and consistency."""
        try:
            # Check for data gaps
            db = next(get_db())
            symbol_obj = self.market_service.data_service.get_symbol_by_name(self.test_symbol_db)
            
            if not symbol_obj:
                logger.error("Symbol not found for integrity check")
                return False
            
            # Check recent data consistency
            latest_data = self.market_service.data_service.get_latest_ohlcv(self.test_symbol_db, count=10)
            
            if not latest_data:
                logger.error("No recent data found for integrity check")
                return False
            
            # Validate OHLC relationships
            for data in latest_data:
                if not (data.low <= data.open <= data.high and 
                       data.low <= data.close <= data.high):
                    logger.error(f"Invalid OHLC relationship at {data.timestamp}")
                    return False
            
            logger.info("✓ Data integrity validated")
            return True
            
        except Exception as e:
            logger.error(f"Data integrity validation failed: {e}")
            return False
        finally:
            if 'db' in locals():
                db.close()
    
    def _validate_performance(self) -> bool:
        """Validate system performance metrics."""
        try:
            # Test query performance
            start_time = time.time()
            
            end_time = datetime.now()
            start_query_time = end_time - timedelta(hours=1)
            
            data = self.market_service.data_service.get_ohlcv_data(
                self.test_symbol_db, start_query_time, end_time, as_dataframe=True
            )
            
            query_duration = time.time() - start_time
            
            if query_duration > 5.0:  # Should be under 5 seconds
                logger.warning(f"Query performance slow: {query_duration:.2f}s")
            
            logger.info(f"✓ Performance validated: Query took {query_duration:.2f}s for {len(data) if data is not None else 0} records")
            return True
            
        except Exception as e:
            logger.error(f"Performance validation failed: {e}")
            return False
    
    def _generate_validation_report(self, passed: int, total: int) -> None:
        """Generate validation report."""
        logger.info("\n" + "=" * 80)
        logger.info("VALIDATION REPORT")
        logger.info("=" * 80)
        
        success_rate = (passed / total) * 100
        logger.info(f"Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            duration = result.get('duration', 0)
            logger.info(f"{status} {test_name} ({duration:.2f}s)")
            
            if 'error' in result:
                logger.info(f"    Error: {result['error']}")
        
        # Save report to file
        report_file = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'success_rate': success_rate,
                'passed_tests': passed,
                'total_tests': total,
                'results': {k: {**v, 'timestamp': v['timestamp'].isoformat()} 
                           for k, v in self.test_results.items()}
            }, f, indent=2)
        
        logger.info(f"Detailed report saved to: {report_file}")
        
        if success_rate == 100:
            logger.info("🎉 ALL VALIDATIONS PASSED - SYSTEM IS PRODUCTION READY!")
        elif success_rate >= 80:
            logger.info("⚠️  MOST VALIDATIONS PASSED - REVIEW FAILED TESTS")
        else:
            logger.info("❌ MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY")


def main():
    """Main validation function."""
    validator = ProductionValidator()
    
    try:
        success = validator.run_validation()
        return success
    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Validation crashed: {e}")
        return False
    finally:
        # Cleanup
        if validator.pipeline:
            validator.pipeline.stop()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
