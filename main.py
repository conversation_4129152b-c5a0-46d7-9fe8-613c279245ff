"""
Main entry point for the Signal Stack Trading Platform.
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.logging import setup_logging
from app.database.connection import check_database_connection
from app.database.init_db import setup_database

# Setup logging
logger = setup_logging(level=settings.log_level)

# Create FastAPI application
app = FastAPI(
    title="Signal Stack Trading Platform",
    description="Scalable Stock Market Trading Platform with Backtesting and Paper Trading",
    version="1.0.0",
    debug=settings.api.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[settings.frontend_url, "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info("Starting Signal Stack Trading Platform...")
    
    # Check database connection
    if not check_database_connection():
        logger.error("Database connection failed. Exiting...")
        exit(1)
    
    # Setup database if needed
    try:
        setup_database()
        logger.info("Database setup completed")
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        exit(1)
    
    logger.info("Application startup completed successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("Shutting down Signal Stack Trading Platform...")


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Signal Stack Trading Platform API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    db_status = check_database_connection()
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "timestamp": "2025-07-12T00:00:00Z"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        log_level=settings.log_level.lower()
    )
