"""
Data service for managing OHLCV data operations.
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import pandas as pd
import logging

from app.database.repositories.symbol_repository import SymbolRepository
from app.database.repositories.ohlcv_repository import OHLCVRepository, OHLCVAggRepository
from app.database.models import Symbol, MarketType
from app.core.logging import get_logger

logger = get_logger(__name__)


class DataService:
    """Service for data operations."""
    
    def __init__(self, db: Session):
        """
        Initialize data service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.symbol_repo = SymbolRepository(db)
        self.ohlcv_repo = OHLCVRepository(db)
        self.ohlcv_agg_repo = OHLCVAggRepository(db)
    
    def create_symbol(self, symbol_data: Dict[str, Any]) -> Symbol:
        """
        Create a new symbol.
        
        Args:
            symbol_data: Symbol data dictionary
            
        Returns:
            Created symbol
        """
        logger.info(f"Creating symbol: {symbol_data.get('symbol')}")
        return self.symbol_repo.create(symbol_data)
    
    def get_symbol_by_name(self, symbol: str) -> Optional[Symbol]:
        """
        Get symbol by name.
        
        Args:
            symbol: Symbol name
            
        Returns:
            Symbol if found, None otherwise
        """
        return self.symbol_repo.get_by_symbol(symbol)
    
    def get_symbols_by_market_type(self, market_type: MarketType) -> List[Symbol]:
        """
        Get symbols by market type.
        
        Args:
            market_type: Market type
            
        Returns:
            List of symbols
        """
        return self.symbol_repo.get_by_market_type(market_type)
    
    def search_symbols(self, search_term: str, limit: int = 50) -> List[Symbol]:
        """
        Search symbols.
        
        Args:
            search_term: Search term
            limit: Maximum results
            
        Returns:
            List of matching symbols
        """
        return self.symbol_repo.search_symbols(search_term, limit)
    
    def bulk_create_symbols(self, symbols_data: List[Dict[str, Any]]) -> List[Symbol]:
        """
        Bulk create symbols.
        
        Args:
            symbols_data: List of symbol data
            
        Returns:
            List of created symbols
        """
        logger.info(f"Bulk creating {len(symbols_data)} symbols")
        return self.symbol_repo.bulk_create_symbols(symbols_data)
    
    def store_ohlcv_data(
        self,
        symbol: str,
        ohlcv_data: List[Dict[str, Any]]
    ) -> bool:
        """
        Store OHLCV data for a symbol.
        
        Args:
            symbol: Symbol name
            ohlcv_data: List of OHLCV data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get symbol
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found: {symbol}")
                return False
            
            # Insert OHLCV data
            count = self.ohlcv_repo.insert_ohlcv_data(symbol_obj.id, ohlcv_data)
            logger.info(f"Stored {count} OHLCV records for {symbol}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to store OHLCV data for {symbol}: {e}")
            return False
    
    def get_ohlcv_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        as_dataframe: bool = False
    ) -> Optional[Any]:
        """
        Get OHLCV data for a symbol.
        
        Args:
            symbol: Symbol name
            start_time: Start timestamp
            end_time: End timestamp
            as_dataframe: Return as pandas DataFrame
            
        Returns:
            OHLCV data as list or DataFrame
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found: {symbol}")
                return None
            
            if as_dataframe:
                return self.ohlcv_repo.get_ohlcv_dataframe(
                    symbol_obj.id, start_time, end_time
                )
            else:
                return self.ohlcv_repo.get_ohlcv_data(
                    symbol_obj.id, start_time, end_time
                )
                
        except Exception as e:
            logger.error(f"Failed to get OHLCV data for {symbol}: {e}")
            return None
    
    def get_latest_ohlcv(self, symbol: str, count: int = 1) -> Optional[List]:
        """
        Get latest OHLCV data for a symbol.
        
        Args:
            symbol: Symbol name
            count: Number of latest records
            
        Returns:
            Latest OHLCV records
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                return None
            
            return self.ohlcv_repo.get_latest_ohlcv(symbol_obj.id, count)
            
        except Exception as e:
            logger.error(f"Failed to get latest OHLCV for {symbol}: {e}")
            return None
    
    def get_aggregated_data(
        self,
        symbol: str,
        timeframe: str,
        start_time: datetime,
        end_time: datetime,
        limit: Optional[int] = None
    ) -> Optional[List]:
        """
        Get aggregated OHLCV data.
        
        Args:
            symbol: Symbol name
            timeframe: Timeframe (5m, 15m, 30m, 1h, 1d)
            start_time: Start timestamp
            end_time: End timestamp
            limit: Maximum records
            
        Returns:
            Aggregated OHLCV data
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                return None
            
            return self.ohlcv_agg_repo.get_aggregated_data(
                symbol_obj.id, timeframe, start_time, end_time, limit
            )
            
        except Exception as e:
            logger.error(f"Failed to get aggregated data for {symbol}: {e}")
            return None
    
    def aggregate_data(
        self,
        symbol: str,
        timeframe: str,
        start_time: datetime,
        end_time: datetime
    ) -> bool:
        """
        Aggregate data for a symbol and timeframe.
        
        Args:
            symbol: Symbol name
            timeframe: Timeframe
            start_time: Start timestamp
            end_time: End timestamp
            
        Returns:
            True if successful, False otherwise
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                return False
            
            self.ohlcv_agg_repo.aggregate_data_using_procedure(
                symbol_obj.id, timeframe, start_time, end_time
            )
            
            logger.info(f"Aggregated data for {symbol} - {timeframe}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to aggregate data for {symbol}: {e}")
            return False
    
    def get_data_statistics(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get data statistics for a symbol.
        
        Args:
            symbol: Symbol name
            
        Returns:
            Statistics dictionary
        """
        try:
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                return None
            
            # Get data range
            data_range = self.ohlcv_repo.get_data_range(symbol_obj.id)
            
            # Get record count
            record_count = self.ohlcv_repo.count({'symbol_id': symbol_obj.id})
            
            # Get latest record
            latest_records = self.ohlcv_repo.get_latest_ohlcv(symbol_obj.id, 1)
            latest_timestamp = latest_records[0].timestamp if latest_records else None
            
            return {
                'symbol': symbol,
                'total_records': record_count,
                'data_range': {
                    'start': data_range[0] if data_range else None,
                    'end': data_range[1] if data_range else None
                },
                'latest_timestamp': latest_timestamp,
                'days_of_data': (data_range[1] - data_range[0]).days if data_range else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get statistics for {symbol}: {e}")
            return None
    
    def cleanup_old_data(self, days_to_keep: int = 90) -> Dict[str, int]:
        """
        Clean up old data.
        
        Args:
            days_to_keep: Number of days to keep
            
        Returns:
            Cleanup statistics
        """
        try:
            deleted_count = self.ohlcv_repo.delete_old_data(days_to_keep)
            logger.info(f"Deleted {deleted_count} old OHLCV records")
            
            return {
                'deleted_records': deleted_count,
                'days_kept': days_to_keep
            }
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            return {'deleted_records': 0, 'days_kept': days_to_keep}
