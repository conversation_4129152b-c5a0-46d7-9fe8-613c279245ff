#!/usr/bin/env python3
"""
Initialize sample data for the Signal Stack Trading Platform.
This script creates sample symbols and generates sample OHLCV data for testing.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import random
import numpy as np

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import setup_logging
from app.database.connection import get_db
from app.services.data_service import DataService
from app.database.models import MarketType

logger = setup_logging(level="INFO")


def create_sample_symbols() -> list:
    """Create sample symbols data."""
    symbols_data = [
        {
            'symbol': 'NIFTY',
            'name': 'Nifty 50',
            'market_type': MarketType.INDEX,
            'exchange': 'NSE',
            'token': 'NSE:NIFTY50-INDEX',
            'lot_size': 50,
            'tick_size': 0.05,
            'is_active': True
        },
        {
            'symbol': 'BANKNIFTY',
            'name': 'Bank Nifty',
            'market_type': MarketType.INDEX,
            'exchange': 'NSE',
            'token': 'NSE:BANKNIFTY-INDEX',
            'lot_size': 25,
            'tick_size': 0.05,
            'is_active': True
        },
        {
            'symbol': 'FINNIFTY',
            'name': 'Fin Nifty',
            'market_type': MarketType.INDEX,
            'exchange': 'NSE',
            'token': 'NSE:FINNIFTY-INDEX',
            'lot_size': 40,
            'tick_size': 0.05,
            'is_active': True
        },
        {
            'symbol': 'RELIANCE',
            'name': 'Reliance Industries Limited',
            'market_type': MarketType.EQUITY,
            'exchange': 'NSE',
            'token': 'NSE:RELIANCE-EQ',
            'lot_size': 1,
            'tick_size': 0.05,
            'is_active': True
        },
        {
            'symbol': 'TCS',
            'name': 'Tata Consultancy Services Limited',
            'market_type': MarketType.EQUITY,
            'exchange': 'NSE',
            'token': 'NSE:TCS-EQ',
            'lot_size': 1,
            'tick_size': 0.05,
            'is_active': True
        },
        {
            'symbol': 'INFY',
            'name': 'Infosys Limited',
            'market_type': MarketType.EQUITY,
            'exchange': 'NSE',
            'token': 'NSE:INFY-EQ',
            'lot_size': 1,
            'tick_size': 0.05,
            'is_active': True
        }
    ]
    
    return symbols_data


def generate_sample_ohlcv_data(
    symbol: str,
    start_price: float,
    days: int = 90,
    volatility: float = 0.02
) -> list:
    """
    Generate sample OHLCV data using random walk.
    
    Args:
        symbol: Symbol name
        start_price: Starting price
        days: Number of days to generate
        volatility: Price volatility factor
        
    Returns:
        List of OHLCV data dictionaries
    """
    logger.info(f"Generating {days} days of sample data for {symbol}")
    
    ohlcv_data = []
    current_price = start_price
    
    # Generate data for each day
    start_date = datetime.now() - timedelta(days=days)
    
    for day in range(days):
        current_date = start_date + timedelta(days=day)
        
        # Skip weekends (Saturday=5, Sunday=6)
        if current_date.weekday() >= 5:
            continue
        
        # Generate intraday data (9:15 AM to 3:30 PM = 375 minutes)
        market_start = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
        
        daily_open = current_price * (1 + random.gauss(0, volatility/4))
        daily_high = daily_open
        daily_low = daily_open
        
        for minute in range(375):  # 375 minutes in trading session
            timestamp = market_start + timedelta(minutes=minute)
            
            # Generate minute-level price movement
            price_change = random.gauss(0, volatility/20)
            new_price = current_price * (1 + price_change)
            
            # Ensure price doesn't go negative
            new_price = max(new_price, 1.0)
            
            # Generate OHLCV for this minute
            minute_open = current_price
            minute_high = max(minute_open, new_price) * (1 + abs(random.gauss(0, volatility/40)))
            minute_low = min(minute_open, new_price) * (1 - abs(random.gauss(0, volatility/40)))
            minute_close = new_price
            minute_volume = random.randint(1000, 50000)
            
            # Update daily high/low
            daily_high = max(daily_high, minute_high)
            daily_low = min(daily_low, minute_low)
            
            ohlcv_data.append({
                'timestamp': timestamp,
                'open': round(minute_open, 2),
                'high': round(minute_high, 2),
                'low': round(minute_low, 2),
                'close': round(minute_close, 2),
                'volume': minute_volume
            })
            
            current_price = minute_close
    
    logger.info(f"Generated {len(ohlcv_data)} OHLCV records for {symbol}")
    return ohlcv_data


def main():
    """Main function to initialize sample data."""
    logger.info("=" * 60)
    logger.info("INITIALIZING SAMPLE DATA")
    logger.info("=" * 60)
    
    try:
        # Get database session
        db = next(get_db())
        data_service = DataService(db)
        
        # Create sample symbols
        logger.info("Creating sample symbols...")
        symbols_data = create_sample_symbols()
        created_symbols = data_service.bulk_create_symbols(symbols_data)
        logger.info(f"Created {len(created_symbols)} symbols")
        
        # Generate sample OHLCV data for each symbol
        symbol_prices = {
            'NIFTY': 19500.0,
            'BANKNIFTY': 44000.0,
            'FINNIFTY': 19800.0,
            'RELIANCE': 2450.0,
            'TCS': 3650.0,
            'INFY': 1580.0
        }
        
        for symbol_name, start_price in symbol_prices.items():
            logger.info(f"Generating sample data for {symbol_name}...")
            
            # Generate 90 days of data
            ohlcv_data = generate_sample_ohlcv_data(
                symbol_name, 
                start_price, 
                days=90,
                volatility=0.02 if symbol_name in ['NIFTY', 'BANKNIFTY', 'FINNIFTY'] else 0.03
            )
            
            # Store the data
            success = data_service.store_ohlcv_data(symbol_name, ohlcv_data)
            if success:
                logger.info(f"Successfully stored data for {symbol_name}")
            else:
                logger.error(f"Failed to store data for {symbol_name}")
        
        # Generate statistics
        logger.info("=" * 60)
        logger.info("DATA STATISTICS")
        logger.info("=" * 60)
        
        for symbol_name in symbol_prices.keys():
            stats = data_service.get_data_statistics(symbol_name)
            if stats:
                logger.info(f"{symbol_name}:")
                logger.info(f"  Total Records: {stats['total_records']}")
                logger.info(f"  Data Range: {stats['data_range']['start']} to {stats['data_range']['end']}")
                logger.info(f"  Days of Data: {stats['days_of_data']}")
                logger.info(f"  Latest: {stats['latest_timestamp']}")
        
        logger.info("Sample data initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Sample data initialization failed: {e}")
        return False
    
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
