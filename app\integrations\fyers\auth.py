"""
Fyers API authentication module.
"""

import os
import time
import webbrowser
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class FyersAuth:
    """Fyers API authentication handler."""
    
    def __init__(self, auth_dir: Optional[str] = None):
        """
        Initialize Fyers authentication.
        
        Args:
            auth_dir: Directory to store authentication files
        """
        self.auth_dir = Path(auth_dir or "auth")
        self.auth_dir.mkdir(exist_ok=True)
        
        self.client_id_file = self.auth_dir / "fyers_client_id.txt"
        self.access_token_file = self.auth_dir / "fyers_access_token.txt"
        
        # Configuration from settings
        self.config = {
            "client_id": settings.fyers.client_id,
            "secret_key": settings.fyers.secret_key,
            "redirect_uri": settings.fyers.redirect_uri,
            "grant_type": "authorization_code",
            "response_type": "code",
            "state": "signal_stack"
        }
    
    def authenticate(self) -> Optional[str]:
        """
        Authenticate with Fyers API and return access token.
        
        Returns:
            Access token if successful, None otherwise
        """
        try:
            # Check for existing valid token
            existing_token = self._get_existing_token()
            if existing_token and self._verify_token(existing_token):
                logger.info("Using existing valid access token")
                return existing_token
            
            logger.info("Starting Fyers authentication process...")
            
            # Import Fyers API
            from fyers_apiv3 import fyersModel
            
            # Create session
            app_session = fyersModel.SessionModel(
                client_id=self.config["client_id"],
                redirect_uri=self.config["redirect_uri"],
                response_type=self.config["response_type"],
                state=self.config["state"],
                secret_key=self.config["secret_key"],
                grant_type=self.config["grant_type"]
            )
            
            # Generate authorization URL
            auth_url = app_session.generate_authcode()
            logger.info(f"Opening browser for authentication: {auth_url}")
            
            # Open browser
            self._open_browser(auth_url)
            
            # Get authorization code from user
            auth_code = self._get_auth_code()
            if not auth_code:
                logger.error("Failed to get authorization code")
                return None
            
            # Generate access token
            app_session.set_token(auth_code)
            token_response = app_session.generate_token()
            
            if token_response and token_response.get("code") == 200:
                access_token = token_response["data"]["access_token"]
                
                # Save tokens
                self._save_tokens(access_token)
                
                logger.info("Fyers authentication successful")
                return access_token
            else:
                logger.error(f"Token generation failed: {token_response}")
                return None
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return None
    
    def _get_existing_token(self) -> Optional[str]:
        """Get existing access token if valid."""
        try:
            if not self.access_token_file.exists():
                return None
            
            # Check if token is from today
            token_mtime = datetime.fromtimestamp(self.access_token_file.stat().st_mtime)
            if token_mtime.date() != datetime.now().date():
                logger.info("Existing token is from a different day")
                return None
            
            with open(self.access_token_file, 'r') as f:
                return f.read().strip()
                
        except Exception as e:
            logger.error(f"Failed to read existing token: {e}")
            return None
    
    def _verify_token(self, access_token: str) -> bool:
        """Verify if access token is valid."""
        try:
            from fyers_apiv3.FyersModel import FyersModel
            
            fyers = FyersModel(
                client_id=self.config["client_id"],
                is_async=False,
                token=access_token
            )
            
            # Test with a simple API call
            response = fyers.get_profile()
            return response and response.get("code") == 200
            
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return False
    
    def _save_tokens(self, access_token: str) -> None:
        """Save access token to file."""
        try:
            with open(self.access_token_file, 'w') as f:
                f.write(access_token)
            
            with open(self.client_id_file, 'w') as f:
                f.write(self.config["client_id"])
                
            logger.info("Tokens saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save tokens: {e}")
    
    def _open_browser(self, url: str) -> None:
        """Open browser with authentication URL."""
        try:
            webbrowser.open(url)
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
            logger.info(f"Please manually open this URL: {url}")
    
    def _get_auth_code(self) -> Optional[str]:
        """Get authorization code from user input."""
        try:
            print("\n" + "="*60)
            print("FYERS AUTHENTICATION")
            print("="*60)
            print("1. A browser window should have opened with the Fyers login page")
            print("2. Log in to your Fyers account")
            print("3. After successful login, you'll be redirected to a URL")
            print("4. Copy the 'auth_code' parameter from the redirected URL")
            print("="*60)
            
            auth_code = input("Enter the authorization code: ").strip()
            
            if not auth_code:
                logger.error("No authorization code provided")
                return None
            
            return auth_code
            
        except KeyboardInterrupt:
            logger.info("Authentication cancelled by user")
            return None
        except Exception as e:
            logger.error(f"Failed to get authorization code: {e}")
            return None
    
    def get_stored_token(self) -> Optional[str]:
        """Get stored access token without verification."""
        try:
            if self.access_token_file.exists():
                with open(self.access_token_file, 'r') as f:
                    return f.read().strip()
        except Exception as e:
            logger.error(f"Failed to read stored token: {e}")
        return None
    
    def clear_tokens(self) -> None:
        """Clear stored tokens."""
        try:
            if self.access_token_file.exists():
                self.access_token_file.unlink()
            if self.client_id_file.exists():
                self.client_id_file.unlink()
            logger.info("Tokens cleared successfully")
        except Exception as e:
            logger.error(f"Failed to clear tokens: {e}")
